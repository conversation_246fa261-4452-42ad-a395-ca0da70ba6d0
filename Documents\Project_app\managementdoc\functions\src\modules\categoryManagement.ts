import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { v4 as uuidv4 } from "uuid";

interface CategoryData {
  name: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
}

interface UpdateCategoryData {
  categoryId: string;
  name?: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
}

interface AddFilesToCategoryData {
  categoryId: string;
  documentIds: string[];
}

interface RemoveFilesFromCategoryData {
  categoryId: string;
  documentIds: string[];
}

/**
 * Create a new category
 */
const createCategory = functions.https.onCall(
  async (data: CategoryData, context) => {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    try {
      const { name, description, permissions, isActive } = data;
      const createdBy = context.auth.uid;

      // Validate required fields
      if (!name || name.trim().length === 0) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Category name is required"
        );
      }

      // Check if category name already exists
      const existingCategory = await admin
        .firestore()
        .collection("categories")
        .where("name", "==", name.trim())
        .where("isActive", "==", true)
        .get();

      if (!existingCategory.empty) {
        throw new functions.https.HttpsError(
          "already-exists",
          "Category with this name already exists"
        );
      }

      // Create category
      const categoryId = uuidv4();
      const categoryData = {
        id: categoryId,
        name: name.trim(),
        description: description?.trim() || "",
        permissions: permissions || [],
        isActive: isActive !== undefined ? isActive : true,
        createdBy,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        documentCount: 0,
      };

      await admin
        .firestore()
        .collection("categories")
        .doc(categoryId)
        .set(categoryData);

      // Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "category_created",
          categoryId,
          userId: createdBy,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `Category "${name}" created`,
        });

      console.log(`Category created successfully: ${categoryId}`);

      return {
        success: true,
        categoryId,
        message: "Category created successfully",
      };
    } catch (error) {
      console.error("Error creating category:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        `Failed to create category: ${error}`
      );
    }
  }
);

/**
 * Update an existing category
 */
const updateCategory = functions.https.onCall(
  async (data: UpdateCategoryData, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    try {
      const { categoryId, name, description, permissions, isActive } = data;
      const updatedBy = context.auth.uid;

      // Validate category exists
      const categoryRef = admin
        .firestore()
        .collection("categories")
        .doc(categoryId);
      const categoryDoc = await categoryRef.get();

      if (!categoryDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Category not found");
      }

      // Check if new name already exists (if name is being updated)
      if (name && name.trim() !== categoryDoc.data()?.name) {
        const existingCategory = await admin
          .firestore()
          .collection("categories")
          .where("name", "==", name.trim())
          .where("isActive", "==", true)
          .get();

        if (!existingCategory.empty) {
          throw new functions.https.HttpsError(
            "already-exists",
            "Category with this name already exists"
          );
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedBy,
      };

      if (name !== undefined) updateData.name = name.trim();
      if (description !== undefined)
        updateData.description = description.trim();
      if (permissions !== undefined) updateData.permissions = permissions;
      if (isActive !== undefined) updateData.isActive = isActive;

      await categoryRef.update(updateData);

      // Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "category_updated",
          categoryId,
          userId: updatedBy,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `Category "${categoryDoc.data()?.name}" updated`,
        });

      console.log(`Category updated successfully: ${categoryId}`);

      return {
        success: true,
        message: "Category updated successfully",
      };
    } catch (error) {
      console.error("Error updating category:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        `Failed to update category: ${error}`
      );
    }
  }
);

/**
 * Delete a category (soft delete)
 */
const deleteCategory = functions.https.onCall(
  async (data: { categoryId: string }, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    try {
      const { categoryId } = data;
      const deletedBy = context.auth.uid;

      // Validate category exists
      const categoryRef = admin
        .firestore()
        .collection("categories")
        .doc(categoryId);
      const categoryDoc = await categoryRef.get();

      if (!categoryDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Category not found");
      }

      const categoryData = categoryDoc.data();

      // Check if category has documents
      const documentsInCategory = await admin
        .firestore()
        .collection("documents")
        .where("category", "==", categoryId)
        .where("isActive", "==", true)
        .get();

      if (!documentsInCategory.empty) {
        // Move documents to uncategorized
        const batch = admin.firestore().batch();
        documentsInCategory.docs.forEach((doc) => {
          batch.update(doc.ref, {
            category: "uncategorized",
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
        });
        await batch.commit();
      }

      // Soft delete category
      await categoryRef.update({
        isActive: false,
        deletedBy,
        deletedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "category_deleted",
          categoryId,
          userId: deletedBy,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `Category "${categoryData?.name}" deleted`,
        });

      console.log(`Category deleted successfully: ${categoryId}`);

      return {
        success: true,
        message: "Category deleted successfully",
        movedDocuments: documentsInCategory.size,
      };
    } catch (error) {
      console.error("Error deleting category:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        `Failed to delete category: ${error}`
      );
    }
  }
);

/**
 * Add files to a category
 */
const addFilesToCategory = functions.https.onCall(
  async (data: AddFilesToCategoryData, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    try {
      const { categoryId, documentIds } = data;
      const userId = context.auth.uid;

      // Validate category exists
      const categoryDoc = await admin
        .firestore()
        .collection("categories")
        .doc(categoryId)
        .get();
      if (!categoryDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Category not found");
      }

      // Update documents in batches
      const batchSize = 500; // Firestore batch limit
      const batches = [];

      for (let i = 0; i < documentIds.length; i += batchSize) {
        const batch = admin.firestore().batch();
        const batchDocumentIds = documentIds.slice(i, i + batchSize);

        for (const documentId of batchDocumentIds) {
          const docRef = admin
            .firestore()
            .collection("documents")
            .doc(documentId);
          batch.update(docRef, {
            category: categoryId,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
        }

        batches.push(batch.commit());
      }

      await Promise.all(batches);

      // Update category document count
      await admin
        .firestore()
        .collection("categories")
        .doc(categoryId)
        .update({
          documentCount: admin.firestore.FieldValue.increment(
            documentIds.length
          ),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      // Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "files_added_to_category",
          categoryId,
          userId,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `${documentIds.length} files added to category "${
            categoryDoc.data()?.name
          }"`,
        });

      console.log(
        `${documentIds.length} files added to category: ${categoryId}`
      );

      return {
        success: true,
        message: `${documentIds.length} files added to category successfully`,
      };
    } catch (error) {
      console.error("Error adding files to category:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        `Failed to add files to category: ${error}`
      );
    }
  }
);

/**
 * Remove files from a category
 */
const removeFilesFromCategory = functions.https.onCall(
  async (data: RemoveFilesFromCategoryData, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    try {
      const { categoryId, documentIds } = data;
      const userId = context.auth.uid;

      // Validate category exists
      const categoryDoc = await admin
        .firestore()
        .collection("categories")
        .doc(categoryId)
        .get();
      if (!categoryDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Category not found");
      }

      // Update documents in batches
      const batchSize = 500;
      const batches = [];

      for (let i = 0; i < documentIds.length; i += batchSize) {
        const batch = admin.firestore().batch();
        const batchDocumentIds = documentIds.slice(i, i + batchSize);

        for (const documentId of batchDocumentIds) {
          const docRef = admin
            .firestore()
            .collection("documents")
            .doc(documentId);
          batch.update(docRef, {
            category: "uncategorized",
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
        }

        batches.push(batch.commit());
      }

      await Promise.all(batches);

      // Update category document count
      await admin
        .firestore()
        .collection("categories")
        .doc(categoryId)
        .update({
          documentCount: admin.firestore.FieldValue.increment(
            -documentIds.length
          ),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      // Log activity
      await admin
        .firestore()
        .collection("activities")
        .add({
          type: "files_removed_from_category",
          categoryId,
          userId,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          details: `${documentIds.length} files removed from category "${
            categoryDoc.data()?.name
          }"`,
        });

      console.log(
        `${documentIds.length} files removed from category: ${categoryId}`
      );

      return {
        success: true,
        message: `${documentIds.length} files removed from category successfully`,
      };
    } catch (error) {
      console.error("Error removing files from category:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        `Failed to remove files from category: ${error}`
      );
    }
  }
);

export const categoryFunctions = {
  createCategory,
  updateCategory,
  deleteCategory,
  addFilesToCategory,
  removeFilesFromCategory,
};
